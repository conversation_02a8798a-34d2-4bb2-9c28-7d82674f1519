use anchor_lang::prelude::*;

#[error_code]
pub enum LbError {
    #[msg("Invalid instruction")]
    InvalidInstruction,
    
    #[msg("Invalid account")]
    InvalidAccount,
    
    #[msg("Invalid bin")]
    InvalidBin,
    
    #[msg("Insufficient liquidity")]
    InsufficientLiquidity,
    
    #[msg("Slippage tolerance exceeded")]
    SlippageExceeded,
    
    #[msg("Invalid amount")]
    InvalidAmount,
    
    #[msg("Pair not initialized")]
    PairNotInitialized,
    
    #[msg("Unauthorized")]
    Unauthorized,
    
    #[msg("Math overflow")]
    MathOverflow,
    
    #[msg("Invalid fee")]
    InvalidFee,
    
    #[msg("Bin array not found")]
    BinArrayNotFound,
    
    #[msg("Invalid bin step")]
    InvalidBinStep,
}
