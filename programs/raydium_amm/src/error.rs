use anchor_lang::prelude::*;

#[error_code]
pub enum AmmError {
    #[msg("Invalid instruction")]
    InvalidInstruction,
    
    #[msg("Invalid account")]
    InvalidAccount,
    
    #[msg("Invalid pool state")]
    InvalidPoolState,
    
    #[msg("Insufficient liquidity")]
    InsufficientLiquidity,
    
    #[msg("Slippage tolerance exceeded")]
    SlippageExceeded,
    
    #[msg("Invalid amount")]
    InvalidAmount,
    
    #[msg("Pool not initialized")]
    PoolNotInitialized,
    
    #[msg("Unauthorized")]
    Unauthorized,
    
    #[msg("Math overflow")]
    MathOverflow,
    
    #[msg("Invalid fee")]
    InvalidFee,
}
