use anchor_lang::prelude::*;

/// Instruction discriminators for Meteora DLMM
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bu<PERSON>, PartialEq)]
pub enum LbInstruction {
    /// Swap instruction
    /// 
    /// Accounts expected:
    /// 0. `[writable]` lb_pair
    /// 1. `[]` bin_array_bitmap_extension (optional)
    /// 2. `[writable]` reserve_x
    /// 3. `[writable]` reserve_y
    /// 4. `[writable]` user_token_x
    /// 5. `[writable]` user_token_y
    /// 6. `[]` token_x_mint
    /// 7. `[]` token_y_mint
    /// 8. `[]` oracle (optional)
    /// 9. `[writable]` host_fee_in (optional)
    /// 10. `[signer]` user
    /// 11. `[]` token_program
    /// 12. `[]` event_authority
    /// 13. `[]` program
    Swap {
        amount_in: u64,
        amount_out_min: u64,
        swap_for_y: bool,
    },
}
