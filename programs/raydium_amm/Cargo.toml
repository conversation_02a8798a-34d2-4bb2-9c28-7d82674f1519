[package]
name = "raydium_amm"
version = "0.3.0"
edition = "2021"

[lib]
crate-type = ["cdylib", "lib"]

[dependencies]
anchor-lang = { version = "0.30.0", features = ["event-cpi"] }
anchor-spl = "0.30.0"
solana-program = "1.18.10"
thiserror = "1.0.58"
arrayref = "0.3.7"
safe-transmute = "0.11.3"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
bytemuck = "1.14.0"
num-traits = "0.2"
num-derive = "0.3"
